{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/image": "1.10.0", "@nuxt/test-utils": "3.19.2", "@nuxthub/core": "0.9.0", "@pinia/nuxt": "0.11.1", "@una-ui/nuxt": "^0.61.0", "nuxt": "^3.17.7", "nuxt-auth-utils": "0.5.20", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@iconify-json/lucide": "^1.2.57", "@iconify-json/openmoji": "^1.2.9", "@iconify-json/ph": "^1.2.2", "@iconify-json/radix-icons": "^1.2.2", "@iconify-json/solar": "^1.2.2", "@iconify-json/tabler": "^1.2.19", "jimp": "^1.6.0", "wrangler": "^4.24.3"}}